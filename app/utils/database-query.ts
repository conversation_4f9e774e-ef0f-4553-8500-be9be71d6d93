// 🦠 BACTERIAL DATABASE QUERY UTILITIES
// Small, pure, self-contained database query building functions

import type { Prisma } from "@prisma/client";
import type { SearchConfig } from "./search";

/**
 * Database provider configuration.
 * Bacterial approach: centralized database provider settings.
 */
export const DATABASE_CONFIG = {
  // Set this to true if using PostgreSQL and want case-insensitive search
  supportsCaseInsensitive: false,
  // Set this to true if using SQLite/MySQL and want to handle case-insensitive search manually
  useManualCaseInsensitive: true
} as const;

/**
 * Create case-insensitive search condition based on database provider.
 * Usage: createSearchCondition('title', 'search term') → Prisma condition
 */
export const createSearchCondition = (field: string, query: string) => {
  if (DATABASE_CONFIG.supportsCaseInsensitive) {
    // PostgreSQL with case-insensitive support
    return { [field]: { contains: query, mode: 'insensitive' } };
  } else if (DATABASE_CONFIG.useManualCaseInsensitive) {
    // SQLite/MySQL - search is case-sensitive by default, but we can work with it
    // Note: This will be case-sensitive, but it's better than breaking
    return { [field]: { contains: query } };
  } else {
    // Fallback: exact case-sensitive search
    return { [field]: { contains: query } };
  }
};

/**
 * Build where clause for task template search.
 * Usage: buildTaskTemplateWhereClause(searchConfig) → Prisma.TaskTemplateWhereInput
 */
export const buildTaskTemplateWhereClause = (
  config: SearchConfig
): Prisma.TaskTemplateWhereInput | undefined => {
  const conditions: Prisma.TaskTemplateWhereInput[] = [];

  // Category filter
  if (config.category && config.category !== 'all') {
    conditions.push({ category: config.category });
  }

  // Search query filter
  if (config.query && config.query.trim()) {
    conditions.push({
      OR: [
        createSearchCondition('title', config.query),
        createSearchCondition('description', config.query),
      ],
    });
  }

  return conditions.length > 0 ? { AND: conditions } : undefined;
};

/**
 * Build pagination options for database query.
 * Usage: buildPaginationOptions(searchConfig) → { skip: number, take: number }
 */
export const buildPaginationOptions = (config: SearchConfig) => {
  const page = config.page || 1;
  const pageSize = config.pageSize || 9;
  
  return {
    skip: (page - 1) * pageSize,
    take: pageSize
  };
};

/**
 * Build order by clause for task templates.
 * Usage: buildTaskTemplateOrderBy() → Prisma.TaskTemplateOrderByWithRelationInput
 */
export const buildTaskTemplateOrderBy = (): Prisma.TaskTemplateOrderByWithRelationInput => {
  return { id: 'asc' };
};

/**
 * Create task template find options.
 * Usage: createTaskTemplateFindOptions(searchConfig) → Prisma.TaskTemplateFindManyArgs
 */
export const createTaskTemplateFindOptions = (
  config: SearchConfig
): Prisma.TaskTemplateFindManyArgs => {
  const whereClause = buildTaskTemplateWhereClause(config);
  const pagination = buildPaginationOptions(config);
  const orderBy = buildTaskTemplateOrderBy();

  return {
    where: whereClause,
    orderBy,
    ...pagination
  };
};

/**
 * Create task template count options.
 * Usage: createTaskTemplateCountOptions(searchConfig) → Prisma.TaskTemplateCountArgs
 */
export const createTaskTemplateCountOptions = (
  config: SearchConfig
): Prisma.TaskTemplateCountArgs => {
  const whereClause = buildTaskTemplateWhereClause(config);
  
  return {
    where: whereClause
  };
};

/**
 * Extract unique values from array.
 * Usage: extractUniqueValues(automations, 'type') → JobType[]
 */
export const extractUniqueValues = <T, K extends keyof T>(
  items: T[],
  key: K
): T[K][] => {
  return Array.from(new Set(items.map(item => item[key])));
};

/**
 * Check if seeding is needed.
 * Usage: needsSeeding(libraryCount, dbCount) → boolean
 */
export const needsSeeding = (libraryCount: number, dbCount: number): boolean => {
  return libraryCount > dbCount;
};

/**
 * Transform database task to display task.
 * Usage: transformDbTaskToDisplay(dbTask) → DisplayTask
 */
export const transformDbTaskToDisplay = <T extends {
  id: number;
  title: string;
  description: string;
  category: string;
  type: any;
  trigger: string;
}>(dbTask: T) => ({
  id: dbTask.id,
  title: dbTask.title,
  description: dbTask.description,
  category: dbTask.category,
  type: dbTask.type,
  trigger: dbTask.trigger,
});

/**
 * Batch transform database tasks to display tasks.
 * Usage: transformDbTasksToDisplay(dbTasks) → DisplayTask[]
 */
export const transformDbTasksToDisplay = <T extends {
  id: number;
  title: string;
  description: string;
  category: string;
  type: any;
  trigger: string;
}>(dbTasks: T[]) => {
  return dbTasks.map(transformDbTaskToDisplay);
};

/**
 * Create automation lookup set.
 * Usage: createAutomationLookupSet(automations) → Set<JobType>
 */
export const createAutomationLookupSet = <T extends { type: any }>(
  automations: T[]
): Set<T['type']> => {
  return new Set(automations.map(a => a.type));
};

/**
 * Check if task is installed.
 * Usage: isTaskInstalled(taskType, installedSet) → boolean
 */
export const isTaskInstalled = <T>(taskType: T, installedSet: Set<T>): boolean => {
  return installedSet.has(taskType);
};

/**
 * Database query result interface.
 */
export interface QueryResult<T> {
  data: T[];
  total: number;
  hasMore: boolean;
}

/**
 * Create query result object.
 * Usage: createQueryResult(data, total, config) → QueryResult<T>
 */
export const createQueryResult = <T>(
  data: T[],
  total: number,
  config: SearchConfig
): QueryResult<T> => {
  const page = config.page || 1;
  const pageSize = config.pageSize || 9;
  const hasMore = (page * pageSize) < total;

  return {
    data,
    total,
    hasMore
  };
};
