import { useCallback, useEffect, useState } from "react";
import { redirect, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigate, useNavigation } from "@remix-run/react";
import { But<PERSON>, Layout, Page, BlockStack, Banner, Text, Card } from "@shopify/polaris";
import { authenticate } from "../shopify.server";
import prisma from "../db.server";
import { JobType, type Automation, type Prisma } from "@prisma/client";
import { TASKS_LIBRARY } from "../data/tasks";
import { AutomationConfigDispatcher } from "../components/automation-configs/AutomationConfigDispatcher";
import { AutomationBasicInfo, SchedulingConfig, useDelayConfig, type DelayConfig } from "../components/AutomationConfigForm";
import { FormErrors, createFormError, type FormError } from "../components/FormErrorHandler";

// Define a more specific type for the automation data used in this route
type TaskTemplate = typeof TASKS_LIBRARY[number];

// Type for the configuration options based on optionsSchema
export type AutomationOptionsConfig = Record<string, any>;

type AutomationConfigData = TaskTemplate &
  Partial<Pick<Automation, 'id' | 'name' | 'status'>> & {
    config?: AutomationOptionsConfig; // Parsed config object
  };

type LoaderData = {
  isNew: boolean;
  automation: AutomationConfigData;
};

type ActionData = {
  errors?: {
    name?: string;
    config?: string;
    general?: string;
  };
};

// LOADER: Fetches data and ensures a consistent object shape is always returned.
export const loader = async ({ request, params }: LoaderFunctionArgs): Promise<Response> => {
  const { session } = await authenticate.admin(request);
  const recordId = parseInt(params.id!, 10);

  if (isNaN(recordId)) {
    throw new Response("Invalid ID", { status: 400 });
  }

  // This function builds a default config object from a task's schema.
  const buildDefaultConfig = (task: TaskTemplate): AutomationOptionsConfig => {
    const config: AutomationOptionsConfig = {};
    if (task.optionsSchema) {
      task.optionsSchema.forEach(opt => {
        // Deep copy the default value to avoid mutation issues.
        config[opt.name] = JSON.parse(JSON.stringify(opt.defaultValue));
      });
    }
    return config;
  };

  // Try to find an existing automation by its own ID.
  const existingAutomation = await prisma.automation.findUnique({
    where: { id: recordId, shop: session.shop },
  });

  if (existingAutomation) {
    const taskTemplate = TASKS_LIBRARY.find(task => task.type === existingAutomation.type);
    if (!taskTemplate) {
      console.error(`Data inconsistency: Automation ID ${existingAutomation.id} for shop ${session.shop} has type ${existingAutomation.type} not found in TASKS_LIBRARY.`);
      throw new Response("Configuration data for this automation type is missing. Please contact support.", { status: 404 });
    }

    // Start with the default config for the template.
    const defaultConfig = buildDefaultConfig(taskTemplate);
    // Safely merge the saved config from the database.
    const dbConfig = existingAutomation.config as AutomationOptionsConfig | null;
    const finalConfig = { ...defaultConfig, ...(dbConfig || {}) };

    const automationData = { ...taskTemplate, ...existingAutomation, config: finalConfig };
    return Response.json({ isNew: false, automation: automationData });
  }

  // If not found, assume the ID is for a new task from the library.
  const taskTemplate = TASKS_LIBRARY.find(task => task.id === recordId);
  if (!taskTemplate) {
    throw new Response("Task template not found.", { status: 404 });
  }

  // For a new automation, always use the default config from the schema.
  const defaultConfig = buildDefaultConfig(taskTemplate);

  return Response.json({ isNew: true, automation: { ...taskTemplate, name: taskTemplate.title, config: defaultConfig } });
};

// ACTION: Handles saving (creating or updating) the automation.
export const action = async ({ request, params }: ActionFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const formData = await request.formData();
  const _action = formData.get("_action") as "create" | "update" | null;
  const name = formData.get("name") as string | null;
  const type = formData.get("type") as JobType | null;
  const trigger = formData.get("trigger") as string | null;
  const configString = formData.get("config") as string | null;

  const automationRecordId = parseInt(params.id!, 10);

  if (!name || name.trim() === "") {
    return Response.json({ errors: { name: "Automation name is required." } }, { status: 400 });
  }

  let configData: Prisma.NullableJsonNullValueInput | Prisma.InputJsonValue | undefined = undefined;

  if (configString) {
    try {
      const parsed = JSON.parse(configString);

      // Type-specific filtering
      if (type === JobType.ORDER_COLLECTION_TAG && Array.isArray(parsed.collections_and_tags)) {
        parsed.collections_and_tags = parsed.collections_and_tags.filter(
          (p: { key: string; value: string }) =>
            p.key.trim() !== "" && p.value.trim() !== ""
        );
      }

      configData = parsed;
    } catch (error) {
      return Response.json({ errors: { config: "Invalid configuration format." } }, { status: 400 });
    }
  }

  try {
    if (_action === "create") {
      if (!type || !trigger) {
        return Response.json({ errors: { general: "Missing type or trigger for new automation." } }, { status: 400 });
      }
      await prisma.automation.create({
        data: { shop: session.shop, name, type, trigger, status: "Active", config: configData }
      });
    } else if (_action === "update") {
      await prisma.automation.update({
        where: { id: automationRecordId, shop: session.shop },
        data: { name, config: configData },
      });
    } else {
      return Response.json({ errors: { general: "Invalid action specified." } }, { status: 400 });
    }

    return redirect("/app/automations");
  } catch (error) {
    console.error(error);
    return Response.json({
      errors: {
        general: "Failed to save automation. Please try again."
      }
    }, { status: 500 });
  }
};

export default function ConfigureAutomation() {
  const { isNew, automation } = useLoaderData<LoaderData>();
  const actionData = useActionData<ActionData>();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const isSaving = navigation.state === 'submitting';

  // 🦠 BACTERIAL STATE MANAGEMENT - Clean separation
  const [name, setName] = useState(automation.name || '');
  const [currentConfig, setCurrentConfig] = useState<AutomationOptionsConfig>(automation.config || {});

  // 🦠 BACTERIAL DELAY CONFIG - Using bacterial hook
  const { delayConfig, updateDelayConfig, isValid: isDelayValid } = useDelayConfig({
    type: automation.config?.delay?.type || 'immediate',
    value: automation.config?.delay?.value,
    unit: automation.config?.delay?.unit || 'hours',
    timestamp: automation.config?.delay?.timestamp
  });

  // 🦠 BACTERIAL ERROR HANDLING - Convert action data to form errors
  const formErrors: FormError[] = [];
  if (actionData?.errors) {
    Object.entries(actionData.errors).forEach(([field, message]) => {
      if (message) {
        formErrors.push(createFormError(message, field === 'general' ? undefined : field));
      }
    });
  }

  useEffect(() => {
    setName(automation.name || '');
    setCurrentConfig(automation.config || {});
  }, [automation]);

  // 🦠 BACTERIAL CONFIG HANDLERS - Clean, focused handlers
  const handleConfigChange = useCallback((newConfig: any) => {
    setCurrentConfig(prevConfig => ({ ...prevConfig, ...newConfig }));
  }, []);

  const handleDelayConfigChange = useCallback((newDelayConfig: DelayConfig) => {
    updateDelayConfig(newDelayConfig);
    setCurrentConfig(prevConfig => ({
      ...prevConfig,
      delay: newDelayConfig
    }));
  }, [updateDelayConfig]);

  return (
    <Page
      title={isNew ? `Configure: ${automation.title}` : `Edit: ${automation.name}`}
      backAction={{ content: "Automations", onAction: () => navigate("/app/automations") }}
    >
      <Layout>
        <Layout.Section>
          {/* 🦠 BACTERIAL ERROR DISPLAY */}
          {formErrors.length > 0 && (
            <FormErrors
              errors={formErrors}
              style="banner"
              title="Please fix the following issues:"
            />
          )}

          {/* 🦠 BACTERIAL INFO BANNER */}
          <Banner title="Automation Setup" tone="info">
            <p>Configure your automation settings below. It will be triggered automatically based on its event type.</p>
          </Banner>
        </Layout.Section>
        <Layout.Section>
          <BlockStack gap="400">
            {/* 🦠 BACTERIAL BASIC INFO COMPONENT */}
            <AutomationBasicInfo
              name={name}
              onNameChange={setName}
              trigger={automation.trigger}
              description={automation.description}
              isNew={isNew}
              nameError={actionData?.errors?.name}
            />

            {/* 🦠 BACTERIAL SCHEDULING COMPONENT */}
            <SchedulingConfig
              delayConfig={delayConfig}
              onDelayConfigChange={handleDelayConfigChange}
              showAdvanced={true}
            />

            {/* 🦠 BACTERIAL AUTOMATION CONFIG */}
            <Card>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  Automation Configuration
                </Text>
                <AutomationConfigDispatcher
                  type={automation.type}
                  config={currentConfig}
                  onConfigChange={handleConfigChange}
                />
              </BlockStack>
            </Card>

            {/* 🦠 BACTERIAL FORM SUBMISSION */}
            <Card>
              <Form method="post">
                <input type="hidden" name="_action" value={isNew ? "create" : "update"} />
                <input type="hidden" name="type" value={automation.type} />
                <input type="hidden" name="trigger" value={automation.trigger} />
                <input type="hidden" name="name" value={name} />
                <input type="hidden" name="config" value={JSON.stringify({ ...currentConfig, delay: delayConfig })} />

                <Button
                  submit
                  variant="primary"
                  loading={isSaving}
                  disabled={!isDelayValid}
                  size="large"
                  fullWidth
                >
                  {isNew ? "Add Automation" : "Save Changes"}
                </Button>
              </Form>
            </Card>
          </BlockStack>
        </Layout.Section>
      </Layout>
      <div style={{ paddingBottom: 'var(--p-space-800)' }} />
    </Page>
  );
}
