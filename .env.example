# Shopify App Configuration
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_API_SECRET=your_api_secret_here
SCOPES=read_products,write_products,read_orders,write_orders,read_customers,write_customers,read_collection_listings,write_collection_listings
SHOPIFY_APP_URL=https://your-app-url.com

# Database Configuration
# MySQL/MariaDB connection string
# Format: mysql://username:password@host:port/database_name
# Example: mysql://user:password@localhost:3306/shopify_app
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# Optional: Custom shop domain (if needed)
# SHOP_CUSTOM_DOMAIN=your-custom-domain.com

# Environment
NODE_ENV=development

# Optional: Frontend port for development
# FRONTEND_PORT=8002
