# Shopify App Configuration
SHOPIFY_API_KEY=your_api_key_here
SHOPIFY_API_SECRET=your_api_secret_here
SCOPES=read_products,write_products,read_orders,write_orders,read_customers,write_customers,read_collection_listings,write_collection_listings
SHOPIFY_APP_URL=https://your-app-url.com

# Database Configuration
# LOCAL DEVELOPMENT:
DATABASE_URL="mysql://username:password@localhost:3307/database_name"

# DOCKER COMPOSE DEPLOYMENT (Shared MySQL container):
# Use the same MySQL container as buytogether app
# DATABASE_URL="mysql://${MYSQL_USER}:${MYSQL_PASSWORD}@buytogether-mysql:3306/automate_boring_stuffs"

# PRODUCTION (External MySQL):
# DATABASE_URL="mysql://username:password@your-mysql-host:3306/database_name"

# MySQL Environment Variables (for shared container)
MYSQL_ROOT_PASSWORD=your_root_password
MYSQL_DATABASE=automate_boring_stuffs
MYSQL_USER=automate_user
MYSQL_PASSWORD=your_password

# Optional: Custom shop domain (if needed)
# SHOP_CUSTOM_DOMAIN=your-custom-domain.com

# Environment
NODE_ENV=development

# Optional: Frontend port for development
# FRONTEND_PORT=8002
