# Production Environment Variables
# Copy this file to .env.production.local and fill in your actual values

# Shopify App Configuration
SHOPIFY_API_KEY=your_production_api_key_here
SHOPIFY_API_SECRET=your_production_api_secret_here
SCOPES=read_products,write_products,read_orders,write_orders,read_customers,write_customers,read_collection_listings,write_collection_listings
SHOPIFY_APP_URL=https://your-production-domain.com

# Database Configuration
# For Docker Compose (using the mysql service)
DATABASE_URL="mysql://shopify:password@mysql:3306/shopify_app"

# For external MySQL database
# DATABASE_URL="mysql://username:password@your-mysql-host:3306/database_name"

# Environment
NODE_ENV=production
