version: "3.8"

services:
  automate-boring-stuffs:
    build: .
    container_name: automate-boring-stuffs
    ports:
      - "3001:3000"  # Using port 3001 to avoid conflict with buytogether app
    env_file:
      - .env
    networks:
      - shopify-buytogether-network
    depends_on:
      - mysql
    restart: always

  # Reference to existing MySQL container
  mysql:
    image: mysql
    container_name: buytogether-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3307:3306"
    networks:
      - shopify-buytogether-network

networks:
  shopify-buytogether-network:
    name: shopify-buytogether-network
    external: true  # Use the existing network created by colleague
